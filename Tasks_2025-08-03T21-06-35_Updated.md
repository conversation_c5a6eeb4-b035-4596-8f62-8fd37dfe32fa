
-[x] NAME:Epic 1: Core Infrastructure Development DESCRIPTION:Temel altyapı bileşenlerinin geliştirilmesi - BaseApiController, DbContext, Entity Framework konfigürasyonları, dependency injection setup'ları AÇIKLAMA:Projenin temel mimari yapılarının kurulması
--[x] NAME:BaseApiController Implementation DESCRIPTION:BaseApiController sınıfının implement edilmesi - SuccessResponse, ErrorResponse, HandleException method'ları, localization desteği (2 saat) AÇIKLAMA:API controller'lar için ortak base sınıfın oluşturulması
--[x] NAME:AcademicPerformanceDbContext Setup DESCRIPTION:Ana PostgreSQL DbContext'in kurulumu - entity configuration'ları, DbSet tanımlamaları, OnModelCreating method'u (3 saat) AÇIKLAMA:Veritabanı bağlantı ve yapılandırma katmanının kurulması
--[x] NAME:Entity Framework Migrations DESCRIPTION:Tüm entity'ler için migration dosyalarının oluşturulması ve veritabanı şemasının kurulumu (4 saat) AÇIKLAMA:Veritabanı şema değişikliklerinin yönetimi
--[x] NAME:Dependency Injection Configuration DESCRIPTION:Program.cs'de tüm service'lerin DI container'a eklenmesi - Manager, Store, Service registrations (2 saat) AÇIKLAMA:Servis bağımlılıklarının otomatik yönetimi
--[x] NAME:MongoDB Configuration DESCRIPTION:MongoDB connection setup, document model'lerin tanımlanması, MongoDbSettings configuration (2 saat) AÇIKLAMA:NoSQL veritabanı bağlantısının kurulması
--[x] NAME:Localization Setup DESCRIPTION:SharedResource.resx dosyalarının oluşturulması, IStringLocalizer configuration, çoklu dil desteği (3 saat) AÇIKLAMA:Çok dilli uygulama desteğinin eklenmesi
--[x] NAME:Authorization Policies Configuration DESCRIPTION:APConsts.Policies tanımlamaları, policy-based authorization setup, JWT configuration (2 saat) AÇIKLAMA:Kullanıcı yetkilendirme sisteminin kurulması
--[x] NAME:Logging Infrastructure DESCRIPTION:IRlxSystemLogHelper integration, structured logging setup, error handling patterns (2 saat) AÇIKLAMA:Sistem günlükleme ve hata yönetimi altyapısı
-[x] NAME:Epic 2: User Management System DESCRIPTION:Kullanıcı yönetimi sistemi - authentication, authorization, user profile management AÇIKLAMA:Kullanıcı kimlik doğrulama ve profil yönetimi sistemi
--[x] NAME:UserController Implementation DESCRIPTION:UserController'da 5 endpoint'in implement edilmesi - GetProfile, GetContext, GetTestProfile, GetUserRoles, GetAcademicCadre (3 saat) AÇIKLAMA:Kullanıcı işlemleri için API endpoint'lerinin geliştirilmesi
--[x] NAME:User Data Service Integration DESCRIPTION:OrganizationManagement API'den kullanıcı verilerinin alınması, UserDataService implementation (2 saat) AÇIKLAMA:Harici sistemlerden kullanıcı bilgilerinin entegrasyonu
--[x] NAME:User Context Helper DESCRIPTION:IUserContextHelper implementation, JWT token'dan user ID extraction, context management (2 saat) AÇIKLAMA:Kullanıcı oturum bilgilerinin yönetimi
--[x] NAME:User Profile DTOs DESCRIPTION:UserProfileDto, UserContextDto, UserDepartmentDto model'lerinin oluşturulması (1 saat) AÇIKLAMA:Kullanıcı veri transfer objelerinin tanımlanması
--[x] NAME:User Authorization Policies DESCRIPTION:User-specific authorization policy'lerinin tanımlanması, APConsts.Policies.AccessAP implementation (1 saat) AÇIKLAMA:Kullanıcı bazlı erişim kontrolü kurallarının belirlenmesi
-[x] NAME:Epic 3: Criteria Management System DESCRIPTION:Kriter yönetim sistemi - dinamik ve statik kriter yönetimi, CRUD operasyonları AÇIKLAMA:Değerlendirme kriterlerinin yönetim sistemi
--[x] NAME:CriteriaController - Dynamic Templates DESCRIPTION:CriteriaController'da dinamik kriter template endpoint'lerinin implement edilmesi - GetDynamicTemplates, GetDynamicTemplate, AddDynamicTemplate, UpdateDynamicTemplate, DeleteDynamicTemplate (4 saat) AÇIKLAMA:Dinamik kriter şablonları için API endpoint'leri
--[x] NAME:CriteriaController - Static Criteria DESCRIPTION:CriteriaController'da statik kriter endpoint'lerinin implement edilmesi - GetStaticCriterion, GetStaticCriterionById, UpdateStaticCriterion (3 saat) AÇIKLAMA:Sabit kriter tanımları için API endpoint'leri
--[x] NAME:CriteriaManager Implementation DESCRIPTION:CriteriaManager business logic implementation - validation, data transformation, MongoDB operations (4 saat) AÇIKLAMA:Kriter yönetimi iş mantığının geliştirilmesi
--[x] NAME:CriteriaStore Implementation DESCRIPTION:CriteriaStore data access layer - PostgreSQL operations, MongoDB operations, CRUD methods (3 saat) AÇIKLAMA:Kriter verilerine erişim katmanının oluşturulması
--[x] NAME:Dynamic Criterion MongoDB Models DESCRIPTION:DynamicCriterionTemplate MongoDB document model'inin oluşturulması, schema validation (2 saat) AÇIKLAMA:Dinamik kriterler için NoSQL veri modellerinin tasarımı
--[x] NAME:Static Criterion Entities DESCRIPTION:StaticCriterionDefinitionEntity, StaticCriterionCoefficientEntity PostgreSQL entity'lerinin oluşturulması (2 saat) AÇIKLAMA:Statik kriterler için veritabanı tablolarının oluşturulması
--[x] NAME:Criteria DTOs and COs DESCRIPTION:CriteriaManagementDtos, GetDynamicCriterionTemplatesCo, GetStaticCriterionDefinitionsCo model'lerinin oluşturulması (2 saat) AÇIKLAMA:Kriter yönetimi için veri transfer objelerinin tanımlanması
--[x] NAME:Criteria Authorization Policies DESCRIPTION:Kriter yönetimi için authorization policy'lerinin tanımlanması - ViewData, ManageCriteria, ViewStaticData (1 saat) AÇIKLAMA:Kriter işlemleri için yetkilendirme kurallarının belirlenmesi
-[x] NAME:Epic 4: Form Management System DESCRIPTION:Form yönetim sistemi - değerlendirme formları, kategori yönetimi, CRUD operasyonları AÇIKLAMA:Değerlendirme formlarının yönetim sistemi
--[x] NAME:FormController - Basic CRUD DESCRIPTION:FormController'da temel CRUD endpoint'lerinin implement edilmesi - GetForms, GetForm, AddForm, UpdateForm, DeleteForm (4 saat) AÇIKLAMA:Form işlemleri için temel API endpoint'leri
--[x] NAME:FormController - Status Management DESCRIPTION:FormController'da durum yönetimi endpoint'lerinin implement edilmesi - GetFormsByStatus, UpdateFormStatus, ActivateForm, ArchiveForm (3 saat) AÇIKLAMA:Form durumlarının yönetimi için API endpoint'leri
--[x] NAME:FormController - Category Management DESCRIPTION:FormController'da kategori yönetimi endpoint'lerinin implement edilmesi - GetCategories, AddCategory, UpdateCategory, DeleteCategory (3 saat) AÇIKLAMA:Form kategorilerinin yönetimi için API endpoint'leri
--[x] NAME:FormController - Criterion Linking DESCRIPTION:FormController'da kriter bağlama endpoint'lerinin implement edilmesi - LinkCriterion, UnlinkCriterion, GetFormCriteria (3 saat) AÇIKLAMA:Form ve kriter ilişkilerinin yönetimi
--[x] NAME:FormController - Advanced Operations DESCRIPTION:FormController'da gelişmiş operasyon endpoint'lerinin implement edilmesi - CloneForm, GetFormStatistics, ValidateForm (3 saat) AÇIKLAMA:Form için gelişmiş işlevlerin API endpoint'leri
--[x] NAME:FormManager Implementation DESCRIPTION:FormManager business logic implementation - form validation, category management, criterion linking logic (4 saat) AÇIKLAMA:Form yönetimi iş mantığının geliştirilmesi
--[x] NAME:FormStore Implementation DESCRIPTION:FormStore data access layer - PostgreSQL operations, complex queries, pagination support (3 saat) AÇIKLAMA:Form verilerine erişim katmanının oluşturulması
--[x] NAME:Form Entities DESCRIPTION:EvaluationFormEntity, FormCategoryEntity, FormCriterionLinkEntity PostgreSQL entity'lerinin oluşturulması (2 saat) AÇIKLAMA:Form için veritabanı tablolarının oluşturulması
--[x] NAME:Form DTOs and COs DESCRIPTION:FormManagementDtos, GetEvaluationFormsCo, GetStatusFilterCo model'lerinin oluşturulması (2 saat) AÇIKLAMA:Form yönetimi için veri transfer objelerinin tanımlanması
--[x] NAME:Form Authorization Policies DESCRIPTION:Form yönetimi için authorization policy'lerinin tanımlanması - ViewReports, ManageForms (1 saat) AÇIKLAMA:Form işlemleri için yetkilendirme kurallarının belirlenmesi
-[x] NAME:Epic 5: Academician Management System DESCRIPTION:Akademisyen yönetim sistemi - profil yönetimi, başvuru sistemi, dashboard AÇIKLAMA:Akademisyen bilgilerinin ve başvurularının yönetim sistemi
--[x] NAME:AcademicianController - Dashboard DESCRIPTION:AcademicianController'da dashboard endpoint'lerinin implement edilmesi - GetDashboard, GetStatistics, GetRecentActivities (3 saat) AÇIKLAMA:Akademisyen dashboard'u için API endpoint'leri
--[x] NAME:AcademicianController - Profile Management DESCRIPTION:AcademicianController'da profil yönetimi endpoint'lerinin implement edilmesi - GetProfile, UpdateProfile, GetAcademicianList (3 saat) AÇIKLAMA:Akademisyen profil yönetimi için API endpoint'leri
--[x] NAME:AcademicianController - Submission Management DESCRIPTION:AcademicianController'da başvuru yönetimi endpoint'lerinin implement edilmesi - GetSubmissions, CreateSubmission, UpdateSubmission, DeleteSubmission (4 saat) AÇIKLAMA:Akademisyen başvuru süreçleri için API endpoint'leri
--[x] NAME:AcademicianController - Data Input DESCRIPTION:AcademicianController'da veri giriş endpoint'lerinin implement edilmesi - InputData, ValidateData, GetInputHistory (3 saat) AÇIKLAMA:Akademisyen veri girişi için API endpoint'leri
--[x] NAME:AcademicianController - Search Operations DESCRIPTION:AcademicianController'da arama operasyonlarının implement edilmesi - SearchSubmissions, SearchAcademicians, GetFilterOptions (3 saat) AÇIKLAMA:Akademisyen arama ve filtreleme işlemleri
--[x] NAME:AcademicianManager Implementation DESCRIPTION:AcademicianManager business logic implementation - profile management, submission validation, data processing (4 saat) AÇIKLAMA:Akademisyen yönetimi iş mantığının geliştirilmesi
--[x] NAME:AcademicianStore Implementation DESCRIPTION:AcademicianStore data access layer - PostgreSQL operations, complex queries, OrganizationManagement API integration (3 saat) AÇIKLAMA:Akademisyen verilerine erişim katmanının oluşturulması
--[x] NAME:Academician Entities DESCRIPTION:AcademicianProfileEntity, AcademicSubmissionEntity PostgreSQL entity'lerinin oluşturulması (2 saat) AÇIKLAMA:Akademisyen için veritabanı tablolarının oluşturulması
--[x] NAME:Submission MongoDB Models DESCRIPTION:AcademicSubmissionDocument, SubmittedPerformanceData MongoDB document model'lerinin oluşturulması (2 saat) AÇIKLAMA:Akademisyen başvuruları için NoSQL veri modellerinin tasarımı
--[x] NAME:Academician DTOs and COs DESCRIPTION:AcademicianDashboardDtos, GetAcademicSubmissionsCo, GetAcademicianFormsCo model'lerinin oluşturulması (2 saat) AÇIKLAMA:Akademisyen yönetimi için veri transfer objelerinin tanımlanması
-[x] NAME:Epic 6: Department Performance System DESCRIPTION:Bölüm performans yönetim sistemi - performans takibi, raporlama, analiz AÇIKLAMA:Bölüm bazında performans ölçümü ve analiz sistemi
--[x] NAME:DepartmentPerformanceController - Basic Operations DESCRIPTION:DepartmentPerformanceController'da temel operasyon endpoint'lerinin implement edilmesi - GetDepartmentPerformance, CreatePerformanceRecord, UpdatePerformanceRecord (3 saat) AÇIKLAMA:Bölüm performansı için temel API endpoint'leri
--[x] NAME:DepartmentPerformanceController - Analytics DESCRIPTION:DepartmentPerformanceController'da analitik endpoint'lerinin implement edilmesi - GetPerformanceAnalytics, GetTrendAnalysis, GetComparativeAnalysis (4 saat) AÇIKLAMA:Bölüm performans analizleri için API endpoint'leri
--[x] NAME:DepartmentPerformanceController - Reporting DESCRIPTION:DepartmentPerformanceController'da raporlama endpoint'lerinin implement edilmesi - GeneratePerformanceReport, ExportPerformanceData, GetReportHistory (3 saat) AÇIKLAMA:Bölüm performans raporları için API endpoint'leri
--[x] NAME:DepartmentPerformanceController - Strategic Indicators DESCRIPTION:DepartmentPerformanceController'da stratejik gösterge endpoint'lerinin implement edilmesi - GetStrategicIndicators, UpdateIndicatorDefinitions (3 saat) AÇIKLAMA:Stratejik performans göstergeleri için API endpoint'leri
--[x] NAME:DepartmentPerformanceManager Implementation DESCRIPTION:DepartmentPerformanceManager business logic implementation - performance calculation, trend analysis, strategic indicator management (4 saat) AÇIKLAMA:Bölüm performansı iş mantığının geliştirilmesi
--[x] NAME:DepartmentPerformanceStore Implementation DESCRIPTION:DepartmentPerformanceStore data access layer - complex performance queries, aggregation operations, historical data management (4 saat) AÇIKLAMA:Bölüm performans verilerine erişim katmanının oluşturulması
--[x] NAME:Department Performance Entities DESCRIPTION:DepartmentPerformanceEntity, DepartmentStrategicIndicatorDefinitionEntity, DepartmentStrategicPerformanceDataEntity PostgreSQL entity'lerinin oluşturulması (3 saat) AÇIKLAMA:Bölüm performansı için veritabanı tablolarının oluşturulması
--[x] NAME:Department Performance DTOs DESCRIPTION:DepartmentPerformanceDtos, DepartmentPerformanceManagerDtos, DepartmentPerformanceStoreDtos model'lerinin oluşturulması (2 saat) AÇIKLAMA:Bölüm performansı için veri transfer objelerinin tanımlanması
-[x] NAME:Epic 7: Portfolio Control System DESCRIPTION:Portföy kontrol sistemi - ders portföy doğrulama, checklist yönetimi AÇIKLAMA:Ders portföylerinin doğrulama ve kontrol sistemi
--[x] NAME:PortfolioControlController - Verification Management DESCRIPTION:PortfolioControlController'da doğrulama yönetimi endpoint'lerinin implement edilmesi - GetPendingVerifications, UpdateVerificationStatus, GetVerificationHistory (3 saat) AÇIKLAMA:Portföy doğrulama süreçleri için API endpoint'leri
--[x] NAME:PortfolioControlController - Checklist Operations DESCRIPTION:PortfolioControlController'da checklist operasyonlarının implement edilmesi - GetChecklistItems, UpdateChecklistItem, CreateCustomChecklist (3 saat) AÇIKLAMA:Portföy kontrol listeleri için API endpoint'leri
--[x] NAME:PortfolioControlController - Statistics DESCRIPTION:PortfolioControlController'da istatistik endpoint'lerinin implement edilmesi - GetVerificationStatistics, GetPortfolioCompletionRates (2 saat) AÇIKLAMA:Portföy kontrol istatistikleri için API endpoint'leri
--[x] NAME:PortfolioControlManager Implementation DESCRIPTION:PortfolioControlManager business logic implementation - verification workflow, checklist management, completion tracking (3 saat) AÇIKLAMA:Portföy kontrol iş mantığının geliştirilmesi
--[x] NAME:PortfolioControlStore Implementation DESCRIPTION:PortfolioControlStore data access layer - verification queries, checklist operations, statistical calculations (3 saat) AÇIKLAMA:Portföy kontrol verilerine erişim katmanının oluşturulması
--[x] NAME:Portfolio Control Entities DESCRIPTION:CoursePortfolioVerificationEntity, PortfolioChecklistItemDefinitionEntity, PortfolioVerificationLogEntity PostgreSQL entity'lerinin oluşturulması (2 saat) AÇIKLAMA:Portföy kontrolü için veritabanı tablolarının oluşturulması
--[x] NAME:Portfolio Control DTOs DESCRIPTION:PortfolioControlDtos model'lerinin oluşturulması (2 saat) AÇIKLAMA:Portföy kontrolü için veri transfer objelerinin tanımlanması
-[x] NAME:Epic 8: Data Verification System DESCRIPTION:Veri doğrulama sistemi - controller dashboard, veri kontrolü, istatistikler AÇIKLAMA:Sistem verilerinin doğrulama ve kalite kontrol sistemi
--[x] NAME:DataVerificationController - Dashboard DESCRIPTION:DataVerificationController'da dashboard endpoint'lerinin implement edilmesi - GetControllerDashboard, GetVerificationOverview (2 saat) AÇIKLAMA:Veri doğrulama dashboard'u için API endpoint'leri
--[x] NAME:DataVerificationController - Statistics DESCRIPTION:DataVerificationController'da istatistik endpoint'lerinin implement edilmesi - GetControllerStatistics, GetDataQualityMetrics (2 saat) AÇIKLAMA:Veri kalitesi istatistikleri için API endpoint'leri
--[x] NAME:DataVerificationController - Verification Operations DESCRIPTION:DataVerificationController'da doğrulama operasyonlarının implement edilmesi - VerifyData, GetVerificationResults (2 saat) AÇIKLAMA:Veri doğrulama işlemleri için API endpoint'leri
--[x] NAME:Data Verification Business Logic DESCRIPTION:Veri doğrulama business logic'inin implement edilmesi - validation rules, data quality checks (3 saat) AÇIKLAMA:Veri doğrulama kurallarının iş mantığının geliştirilmesi
--[x] NAME:Data Verification DTOs DESCRIPTION:Data verification için gerekli DTO model'lerinin oluşturulması (1 saat) AÇIKLAMA:Veri doğrulama için veri transfer objelerinin tanımlanması
-[x] NAME:Epic 9: File Upload System DESCRIPTION:Dosya yükleme sistemi - MinIO entegrasyonu, dosya doğrulama, metadata yönetimi AÇIKLAMA:Dosya yükleme ve depolama yönetim sistemi
--[x] NAME:FileUploadController - Basic Upload DESCRIPTION:FileUploadController'da temel dosya yükleme endpoint'lerinin implement edilmesi - UploadFile, UploadMultipleFiles, GetUploadStatus (3 saat) AÇIKLAMA:Temel dosya yükleme işlemleri için API endpoint'leri
--[x] NAME:FileUploadController - File Management DESCRIPTION:FileUploadController'da dosya yönetimi endpoint'lerinin implement edilmesi - GetFileInfo, DeleteFile, DownloadFile, GetFileList (3 saat) AÇIKLAMA:Dosya yönetimi işlemleri için API endpoint'leri
--[x] NAME:FileUploadController - Validation DESCRIPTION:FileUploadController'da dosya doğrulama endpoint'lerinin implement edilmesi - ValidateFile, GetValidationRules, CheckFileIntegrity (3 saat) AÇIKLAMA:Dosya doğrulama işlemleri için API endpoint'leri
--[x] NAME:FileUploadController - Metadata Management DESCRIPTION:FileUploadController'da metadata yönetimi endpoint'lerinin implement edilmesi - UpdateFileMetadata, GetFileMetadata, SearchFiles (3 saat) AÇIKLAMA:Dosya metadata yönetimi için API endpoint'leri
--[x] NAME:FileUploadController - Batch Operations DESCRIPTION:FileUploadController'da toplu operasyon endpoint'lerinin implement edilmesi - BatchUpload, BatchDelete, BatchValidate (3 saat) AÇIKLAMA:Toplu dosya işlemleri için API endpoint'leri
--[x] NAME:MinIO Service Integration DESCRIPTION:MinIO dosya depolama servisinin entegrasyonu - connection setup, bucket management, file operations (4 saat) AÇIKLAMA:Bulut dosya depolama sisteminin entegrasyonu
--[x] NAME:File Upload Business Logic DESCRIPTION:Dosya yükleme business logic'inin implement edilmesi - validation, virus scanning, metadata extraction (3 saat) AÇIKLAMA:Dosya yükleme iş mantığının geliştirilmesi
--[x] NAME:Evidence File Entities DESCRIPTION:EvidenceFileEntity PostgreSQL entity'sinin oluşturulması ve EvidenceFileStore implementation (2 saat) AÇIKLAMA:Kanıt dosyaları için veritabanı tablolarının oluşturulması
--[x] NAME:File Upload DTOs DESCRIPTION:EvidenceFileDto ve diğer file upload DTO model'lerinin oluşturulması (2 saat) AÇIKLAMA:Dosya yükleme için veri transfer objelerinin tanımlanması
-[x] NAME:Epic 10: Notification System DESCRIPTION:Bildirim sistemi - email notifications, template management, SMTP integration AÇIKLAMA:E-posta bildirimleri ve şablon yönetim sistemi
--[x] NAME:NotificationController - Email Operations DESCRIPTION:NotificationController'da email operasyon endpoint'lerinin implement edilmesi - SendEmail, SendBulkEmail, GetEmailStatus (3 saat) AÇIKLAMA:E-posta gönderimi için API endpoint'leri
--[x] NAME:NotificationController - Template Management DESCRIPTION:NotificationController'da template yönetimi endpoint'lerinin implement edilmesi - GetTemplates, CreateTemplate, UpdateTemplate, DeleteTemplate (3 saat) AÇIKLAMA:E-posta şablonları yönetimi için API endpoint'leri
--[x] NAME:NotificationController - Notification History DESCRIPTION:NotificationController'da bildirim geçmişi endpoint'lerinin implement edilmesi - GetNotificationHistory, GetDeliveryStatus (2 saat) AÇIKLAMA:Bildirim geçmişi takibi için API endpoint'leri
--[x] NAME:NotificationController - Health Check DESCRIPTION:NotificationController'da sağlık kontrolü endpoint'inin implement edilmesi - CheckHealth, TestSmtpConnection (2 saat) AÇIKLAMA:Bildirim sistemi sağlık kontrolü için API endpoint'leri
--[x] NAME:SMTP Service Integration DESCRIPTION:SMTP email servisinin entegrasyonu - configuration, connection management, email sending logic (3 saat) AÇIKLAMA:E-posta sunucu entegrasyonunun kurulması
--[x] NAME:Email Template Engine DESCRIPTION:Email template engine'inin implement edilmesi - template parsing, variable substitution, HTML generation (3 saat) AÇIKLAMA:E-posta şablon motorunun geliştirilmesi
--[x] NAME:Notification Business Logic DESCRIPTION:Bildirim business logic'inin implement edilmesi - notification rules, delivery scheduling, retry logic (3 saat) AÇIKLAMA:Bildirim sistemi iş mantığının geliştirilmesi
--[x] NAME:Notification DTOs DESCRIPTION:NotificationDtos model'lerinin oluşturulması (1 saat) AÇIKLAMA:Bildirim sistemi için veri transfer objelerinin tanımlanması
-[x] NAME:Epic 11: Reporting System DESCRIPTION:Raporlama sistemi - çeşitli raporlar, export functionality, dashboard AÇIKLAMA:Kapsamlı raporlama ve veri dışa aktarma sistemi
--[x] NAME:ReportingController - Basic Reports DESCRIPTION:ReportingController'da temel rapor endpoint'lerinin implement edilmesi - GenerateReport, GetReportList, GetReportById (3 saat) AÇIKLAMA:Temel raporlama işlemleri için API endpoint'leri
--[x] NAME:ReportingController - Export Operations DESCRIPTION:ReportingController'da export operasyon endpoint'lerinin implement edilmesi - ExportToPdf, ExportToExcel, ExportToCsv (3 saat) AÇIKLAMA:Rapor dışa aktarma işlemleri için API endpoint'leri
--[x] NAME:ReportingController - Dashboard Reports DESCRIPTION:ReportingController'da dashboard rapor endpoint'lerinin implement edilmesi - GetDashboardData, GetKpiReports, GetTrendReports (3 saat) AÇIKLAMA:Dashboard raporları için API endpoint'leri
--[x] NAME:ReportingController - Custom Reports DESCRIPTION:ReportingController'da özel rapor endpoint'lerinin implement edilmesi - CreateCustomReport, GetCustomReportTemplates (2 saat) AÇIKLAMA:Özelleştirilebilir raporlar için API endpoint'leri
--[x] NAME:ReportingManager Implementation DESCRIPTION:ReportingManager business logic implementation - report generation, data aggregation, formatting (4 saat) AÇIKLAMA:Raporlama sistemi iş mantığının geliştirilmesi
--[x] NAME:ReportingStore Implementation DESCRIPTION:ReportingStore data access layer - complex reporting queries, data aggregation, historical data access (4 saat) AÇIKLAMA:Raporlama verilerine erişim katmanının oluşturulması
--[x] NAME:Report Export Services DESCRIPTION:PDF, Excel, CSV export servislerinin implement edilmesi - formatting, styling, data transformation (3 saat) AÇIKLAMA:Çoklu format rapor dışa aktarma servislerinin geliştirilmesi
--[x] NAME:Reporting DTOs DESCRIPTION:ReportingStoreDtos, ReportExportDto, PerformanceReportDto, AcademicianReportDto, DepartmentReportDto model'lerinin oluşturulması (2 saat) AÇIKLAMA:Raporlama sistemi için veri transfer objelerinin tanımlanması
-[x] NAME:Epic 12: Staff Competency System DESCRIPTION:Personel yetkinlik sistemi - yetkinlik değerlendirme, istatistiksel analiz, raporlama AÇIKLAMA:Personel yetkinlik değerlendirme ve analiz sistemi
--[x] NAME:StaffCompetencyController - Statistical Operations DESCRIPTION:StaffCompetencyController'da istatistiksel operasyon endpoint'lerinin implement edilmesi - Epic 1 task'ları (5 endpoint, 4 saat) AÇIKLAMA:Yetkinlik istatistikleri için API endpoint'leri
--[x] NAME:StaffCompetencyController - Query Operations DESCRIPTION:StaffCompetencyController'da sorgu operasyon endpoint'lerinin implement edilmesi - Epic 2 task'ları (4 endpoint, 3 saat) AÇIKLAMA:Yetkinlik sorguları için API endpoint'leri
--[x] NAME:StaffCompetencyController - Validation Operations DESCRIPTION:StaffCompetencyController'da doğrulama operasyon endpoint'lerinin implement edilmesi - Epic 3 task'ları (8 endpoint, 4 saat) AÇIKLAMA:Yetkinlik doğrulama işlemleri için API endpoint'leri
--[x] NAME:StaffCompetencyController - Form Operations DESCRIPTION:StaffCompetencyController'da form operasyon endpoint'lerinin implement edilmesi - Epic 4 task'ları (5 endpoint, 3 saat) AÇIKLAMA:Yetkinlik formları için API endpoint'leri
--[x] NAME:StaffCompetencyController - Test Endpoints DESCRIPTION:StaffCompetencyController'da test endpoint'lerinin implement edilmesi - Epic 6 task'ları (6 test endpoint, 2 saat) AÇIKLAMA:Yetkinlik sistemi test endpoint'leri
--[x] NAME:StaffCompetencyManager Implementation DESCRIPTION:StaffCompetencyManager business logic implementation - yetkinlik hesaplama, trend analizi, karşılaştırma (5 saat) AÇIKLAMA:Personel yetkinlik sistemi iş mantığının geliştirilmesi
--[x] NAME:StaffCompetencyStore Implementation DESCRIPTION:StaffCompetencyStore data access layer - complex competency queries, statistical calculations, performance optimizations (4 saat) AÇIKLAMA:Yetkinlik verilerine erişim katmanının oluşturulması
--[x] NAME:Staff Competency Entities DESCRIPTION:StaffCompetencyDefinitionEntity, StaffCompetencyEvaluationEntity, CompetencyRatingEntity PostgreSQL entity'lerinin oluşturulması (3 saat) AÇIKLAMA:Personel yetkinliği için veritabanı tablolarının oluşturulması
--[x] NAME:Staff Competency Cache Service DESCRIPTION:StaffCompetencyCacheService implement edilmesi - performance optimization için cache layer (2 saat) AÇIKLAMA:Yetkinlik sistemi performans optimizasyonu için önbellek katmanı
--[x] NAME:Staff Competency DTOs DESCRIPTION:StaffCompetencyDtos model'lerinin oluşturulması (2 saat) AÇIKLAMA:Personel yetkinliği için veri transfer objelerinin tanımlanması
-[x] NAME:Epic 13: Generic Data Entry System DESCRIPTION:Genel veri giriş sistemi - dinamik form tanımlama, veri giriş, raporlama AÇIKLAMA:Esnek veri giriş ve dinamik form yönetim sistemi
--[x] NAME:GenericDataEntryController - Definition Management DESCRIPTION:GenericDataEntryController'da tanım yönetimi endpoint'lerinin implement edilmesi - CreateDefinition, UpdateDefinition, DeleteDefinition, GetDefinitions (4 saat) AÇIKLAMA:Dinamik form tanımları için API endpoint'leri
--[x] NAME:GenericDataEntryController - Record Management DESCRIPTION:GenericDataEntryController'da kayıt yönetimi endpoint'lerinin implement edilmesi - CreateRecord, UpdateRecord, DeleteRecord, GetRecords (4 saat) AÇIKLAMA:Dinamik form kayıtları için API endpoint'leri
--[x] NAME:GenericDataEntryController - Search Operations DESCRIPTION:GenericDataEntryController'da arama operasyon endpoint'lerinin implement edilmesi - SearchRecords, GetFilterOptions, AdvancedSearch (3 saat) AÇIKLAMA:Dinamik form arama işlemleri için API endpoint'leri
--[x] NAME:GenericDataEntryController - Statistics DESCRIPTION:GenericDataEntryController'da istatistik endpoint'lerinin implement edilmesi - GetStatistics, GetOverallStatistics, GetUsageMetrics (2 saat) AÇIKLAMA:Dinamik form istatistikleri için API endpoint'leri
--[x] NAME:GenericDataEntryManager Implementation DESCRIPTION:GenericDataEntryManager business logic implementation - dynamic form validation, data processing, statistics calculation (4 saat) AÇIKLAMA:Genel veri giriş sistemi iş mantığının geliştirilmesi
--[x] NAME:GenericDataEntryStore Implementation DESCRIPTION:GenericDataEntryStore data access layer - dynamic schema operations, complex queries, aggregation (4 saat) AÇIKLAMA:Dinamik veri girişi verilerine erişim katmanının oluşturulması
--[x] NAME:Generic Data Entry Entities DESCRIPTION:GenericDataEntryDefinitionEntity, GenericDataEntryRecordEntity PostgreSQL entity'lerinin oluşturulması (2 saat) AÇIKLAMA:Genel veri girişi için veritabanı tablolarının oluşturulması
--[x] NAME:Generic Data Entry DTOs DESCRIPTION:GenericDataEntryDtos, GenericDataEntryCos model'lerinin oluşturulması (2 saat) AÇIKLAMA:Genel veri girişi için veri transfer objelerinin tanımlanması
-[x] NAME:Epic 14: Additional Controllers DESCRIPTION:Ek controller'ların geliştirilmesi - ControllerDashboardController, StaticCriterionDataController geliştirmeleri AÇIKLAMA:Sistem yönetimi için ek controller'ların geliştirilmesi
--[x] NAME:ControllerDashboardController Implementation DESCRIPTION:ControllerDashboardController'da dashboard endpoint'lerinin implement edilmesi - GetDashboard, GetControllerMetrics, GetSystemOverview (3 saat) AÇIKLAMA:Sistem yöneticisi dashboard'u için API endpoint'leri
--[x] NAME:StaticCriterionDataController - Extended Operations DESCRIPTION:StaticCriterionDataController'da genişletilmiş operasyon endpoint'lerinin implement edilmesi - GetAcademicianStaticCriteria, GetSpecificStaticCriteria, GetBatchStaticCriteria, GetPerformanceMetrics, CheckHealth (4 saat) AÇIKLAMA:Statik kriter verileri için genişletilmiş API endpoint'leri
--[x] NAME:Controller Dashboard Business Logic DESCRIPTION:Controller dashboard business logic'inin implement edilmesi - metrics calculation, system monitoring (2 saat) AÇIKLAMA:Sistem dashboard'u iş mantığının geliştirilmesi
--[x] NAME:Static Criterion Data Service DESCRIPTION:Static criterion data service'inin implement edilmesi - data aggregation, performance metrics (3 saat) AÇIKLAMA:Statik kriter veri servisinin geliştirilmesi
